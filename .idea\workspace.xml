<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="c4f8d0f4-dc8e-4c79-89a4-a4066eeef15d" name="更改" comment="第一次提交">
      <change afterPath="$PROJECT_DIR$/打脸修真路/原文/打脸1.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/打脸修真路/原文/打脸10.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/打脸修真路/原文/打脸11.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/打脸修真路/原文/打脸12.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/打脸修真路/原文/打脸2.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/打脸修真路/原文/打脸3.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/打脸修真路/原文/打脸4.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/打脸修真路/原文/打脸5.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/打脸修真路/原文/打脸6.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/打脸修真路/原文/打脸7.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/打脸修真路/原文/打脸8.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/打脸修真路/原文/打脸9.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/修改设定.md" beforeDir="false" afterPath="$PROJECT_DIR$/权倾天下：废王的逆袭/修改设定01-02.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/原文.txt" beforeDir="false" afterPath="$PROJECT_DIR$/权倾天下：废王的逆袭/原文01-02.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/权倾天下：废王的逆袭（快节奏版）.txt" beforeDir="false" afterPath="$PROJECT_DIR$/权倾天下：废王的逆袭/权倾天下：废王的逆袭（快节奏版-对应原文01-02章）.txt" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="31Xko5VcRHAjbPlszmcVaXVTS8K" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Python.docx_to_txt.executor": "Run",
    "Python.pdf_to_txt.executor": "Run",
    "Python.word_to_txt.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "master",
    "last_opened_file_path": "D:/pythonProject/MyProject/novel/打脸修真路",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\pythonProject\MyProject\novel\打脸修真路" />
      <recent name="D:\pythonProject\MyProject\novel" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\pythonProject\MyProject\novel\打脸修真路\原文" />
      <recent name="D:\pythonProject\MyProject\novel\权倾天下：废王的逆袭" />
    </key>
  </component>
  <component name="RunManager" selected="Python.word_to_txt">
    <configuration name="docx_to_txt" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="novel" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/docx_to_txt.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="pdf_to_txt" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="novel" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/pdf_to_txt.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="word_to_txt" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="novel" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/word_to_txt.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.word_to_txt" />
        <item itemvalue="Python.docx_to_txt" />
        <item itemvalue="Python.pdf_to_txt" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-PY-241.18034.82" />
        <option value="bundled-python-sdk-975db3bf15a3-2767605e8bc2-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-241.18034.82" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="c4f8d0f4-dc8e-4c79-89a4-a4066eeef15d" name="更改" comment="" />
      <created>1755677976957</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1755677976957</updated>
      <workItem from="1755677978044" duration="4603000" />
      <workItem from="1755750631831" duration="6398000" />
    </task>
    <task id="LOCAL-00001" summary="第一次提交">
      <option name="closed" value="true" />
      <created>1755678556793</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1755678556793</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="第一次提交" />
    <option name="LAST_COMMIT_MESSAGE" value="第一次提交" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/novel$pdf_to_txt.coverage" NAME="pdf_to_txt 覆盖结果" MODIFIED="1755678054752" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/novel$docx_to_txt.coverage" NAME="docx_to_txt 覆盖结果" MODIFIED="1755678148002" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/novel$word_to_txt.coverage" NAME="word_to_txt 覆盖结果" MODIFIED="1755680698617" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
  </component>
</project>